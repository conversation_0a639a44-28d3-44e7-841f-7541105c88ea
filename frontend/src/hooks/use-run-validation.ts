'use client';

import { useSubscription } from '@/hooks/react-query/subscriptions/use-subscriptions';
import { isLocalMode } from '@/lib/config';

export interface RunValidationResult {
  canSubmit: boolean;
  reason?: string;
  runsRemaining?: number;
  isFreePlan?: boolean;
}

export const useRunValidation = (): RunValidationResult => {
  const { data: subscriptionData } = useSubscription();

  // In local mode, always allow runs
  if (isLocalMode()) {
    return {
      canSubmit: true,
      runsRemaining: 999,
      isFreePlan: false,
    };
  }

  // If no subscription data yet, be conservative and block
  if (!subscriptionData) {
    return {
      canSubmit: false,
      reason: 'Loading subscription data...',
    };
  }

  const isFreePlan = subscriptionData.status !== 'active';
  const currentUsage = subscriptionData.current_usage || 0;
  const messagesLimit = subscriptionData.messages_limit || 0;
  const runsRemaining = Math.max(0, messagesLimit - currentUsage);

  // Check if user has runs remaining
  if (runsRemaining <= 0) {
    return {
      canSubmit: false,
      reason: isFreePlan 
        ? 'You have used all your free runs. Upgrade to continue using Atlas.'
        : 'You have reached your monthly run limit. Your limit will reset next month.',
      runsRemaining: 0,
      isFreePlan,
    };
  }

  return {
    canSubmit: true,
    runsRemaining,
    isFreePlan,
  };
};
