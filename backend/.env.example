# Copy this file to .env and fill in your values

# Environment Mode
# Valid values: local, staging, production
ENV_MODE=local

#DATABASE
SUPABASE_URL=
SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=

REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_SSL=false

RABBITMQ_HOST=rabbitmq
RABBITMQ_PORT=5672

# LLM Providers:
ANTHROPIC_API_KEY=
OPENAI_API_KEY=
MODEL_TO_USE=

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION_NAME=

GROQ_API_KEY=
OPENROUTER_API_KEY=

# DATA APIS
RAPID_API_KEY=

# WEB SEARCH
TAVILY_API_KEY=

# WEB SCRAPE
FIRECRAWL_API_KEY=
FIRECRAWL_URL=

# Sandbox container provider:
DAYTONA_API_KEY=
DAYTONA_SERVER_URL=
DAYTONA_TARGET=

LANGFUSE_PUBLIC_KEY="pk-REDACTED"
LANGFUSE_SECRET_KEY="sk-REDACTED"
LANGFUSE_HOST="https://cloud.langfuse.com"

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_YOUR_SECRET_KEY_HERE
STRIPE_WEBHOOK_SECRET=whsec_YOUR_WEBHOOK_SECRET_HERE
STRIPE_PRODUCT_ID=prod_YOUR_PRODUCT_ID_HERE

SMITHERY_API_KEY=


Our Stripe is currently all in test mode, including product IDs and everything else that has been implemented for billing.

I want to move to production mode with the new Stripe in production mode so my users can actually be set up. There are a few caveats that I want to watch out for before I make the switch.

I want you to scan the entire codebase using Context Engine and Memories to make sure that there is nothing that we have missed for the billing integration since we are fully launching billing now to production and to users. I just want to make sure there is nothing that we missed or overlooked first.

Once you check everything, make a comprehensive list of anything that you think we have missed or overlooked with the billing feature so we can get ready for production.

Your job is to make two lists: - Everything that you think we might have missed to get ready for the production version of this application, so our users don't feel any discrepancies and can successfully use the application. - To check the entire codebase to make sure we didn't miss anything with our new integration.

And make another list which will be only for Stripe and all the IDs and secrets etc that need to be updated for production mode. Since Stripe is also currently in test mode in this codebase, just make these two lists and get back to me.

One more thing that I need your help with for Stripe is to include this in the Stripe list: there are some users who have already paid us through a different Stripe product. I want to add them to the Pro tier of our application without them having to pay again or without them feeling any discrepancy at all. How can we achieve this?
